/*
 * Copyright (c) 2024 Proton AG
 *
 * This file is part of ProtonVPN.
 *
 * ProtonVPN is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * ProtonVPN is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with ProtonVPN.  If not, see <https://www.gnu.org/licenses/>.
 */

using Microsoft.VisualStudio.TestTools.UnitTesting;
using NSubstitute;
using ProtonVPN.Client.EventMessaging.Contracts;
using ProtonVPN.Client.Logic.Connection.Contracts;
using ProtonVPN.Client.Logic.Connection.Contracts.Enums;
using ProtonVPN.Client.Logic.Connection.Contracts.Messages;
using ProtonVPN.Client.Settings.Contracts;
using ProtonVPN.Client.Settings.Contracts.Models;
using ProtonVPN.IssueReporting.Contracts;
using ProtonVPN.Logging.Contracts;

namespace ProtonVPN.Client.Logic.Connection.Tests;

[TestClass]
public class ChangeServerModeratorTest
{
    private ILogger _logger = null!;
    private IIssueReporter _issueReporter = null!;
    private ISettings _settings = null!;
    private IConnectionManager _connectionManager = null!;
    private IEventMessageSender _eventMessageSender = null!;
    private ChangeServerModerator _changeServerModerator = null!;

    [TestInitialize]
    public void TestInitialize()
    {
        _logger = Substitute.For<ILogger>();
        _issueReporter = Substitute.For<IIssueReporter>();
        _settings = Substitute.For<ISettings>();
        _connectionManager = Substitute.For<IConnectionManager>();
        _eventMessageSender = Substitute.For<IEventMessageSender>();

        // Setup default settings
        _settings.ChangeServerSettings.Returns(new ChangeServerSettings
        {
            AttemptsLimit = 3,
            ShortDelay = TimeSpan.FromSeconds(6),
            LongDelay = TimeSpan.FromMinutes(15)
        });

        _settings.ChangeServerAttempts.Returns(new ChangeServerAttempts
        {
            AttemptsCount = 0,
            LastAttemptUtcDate = DateTimeOffset.UtcNow.AddMinutes(-10)
        });

        _changeServerModerator = new ChangeServerModerator(
            _logger,
            _issueReporter,
            _settings,
            _connectionManager,
            _eventMessageSender);
    }

    [TestCleanup]
    public void TestCleanup()
    {
        // No cleanup needed for ChangeServerModerator
    }

    [TestMethod]
    public void HasTroubleConnecting_WhenNotConnecting_ShouldReturnFalse()
    {
        // Arrange - default state (not connecting)

        // Act
        bool result = _changeServerModerator.HasTroubleConnecting();

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public void HasTroubleConnecting_WhenConnectingButLessThan6Seconds_ShouldReturnFalse()
    {
        // Arrange
        var message = new ConnectionStatusChangedMessage(ConnectionStatus.Connecting);
        _changeServerModerator.Receive(message);

        // Act
        bool result = _changeServerModerator.HasTroubleConnecting();

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public void HasTroubleConnecting_WhenConnectingForMoreThan6Seconds_ShouldReturnTrue()
    {
        // Arrange
        var message = new ConnectionStatusChangedMessage(ConnectionStatus.Connecting);
        _changeServerModerator.Receive(message);

        // Simulate 7 seconds passing by manually setting the connection start time
        var field = typeof(ChangeServerModerator).GetField("_connectionStartTime", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        field?.SetValue(_changeServerModerator, DateTimeOffset.UtcNow.AddSeconds(-7));

        // Act
        bool result = _changeServerModerator.HasTroubleConnecting();

        // Assert
        Assert.IsTrue(result);
    }

    [TestMethod]
    public void HasTroubleConnecting_WhenConnectedAfterConnecting_ShouldReturnFalse()
    {
        // Arrange
        var connectingMessage = new ConnectionStatusChangedMessage(ConnectionStatus.Connecting);
        _changeServerModerator.Receive(connectingMessage);

        var connectedMessage = new ConnectionStatusChangedMessage(ConnectionStatus.Connected);
        _changeServerModerator.Receive(connectedMessage);

        // Act
        bool result = _changeServerModerator.HasTroubleConnecting();

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public void HasTroubleConnecting_WhenDisconnectedAfterConnecting_ShouldReturnFalse()
    {
        // Arrange
        var connectingMessage = new ConnectionStatusChangedMessage(ConnectionStatus.Connecting);
        _changeServerModerator.Receive(connectingMessage);

        var disconnectedMessage = new ConnectionStatusChangedMessage(ConnectionStatus.Disconnected);
        _changeServerModerator.Receive(disconnectedMessage);

        // Act
        bool result = _changeServerModerator.HasTroubleConnecting();

        // Assert
        Assert.IsFalse(result);
    }
}
